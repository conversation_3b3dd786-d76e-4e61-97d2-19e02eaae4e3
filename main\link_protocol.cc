#include <stdio.h>
#include <vector>
#include <string>
#include <mutex>
#include <algorithm>
#include <cctype>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_event.h"
#include "nvs_flash.h"
#include "esp_log.h"
#include "nimble/nimble_port.h"
#include "nimble/nimble_port_freertos.h"
#include "host/ble_hs.h"
#include "services/gap/ble_svc_gap.h"
#include "host/ble_uuid.h"
#include "host/ble_gap.h"
#include "sdkconfig.h"
#include <cJSON.h>
#include "esp_random.h"
#include <sys/time.h>

#if CONFIG_BT_NIMBLE_EXT_ADV
#include "host/ble_gap.h"
#include "os/os_mbuf.h"
#include "nimble/ble.h"
#endif

extern uint8_t ble_addr_type;
static int scan_device_count = 0;
static const int MAX_SCAN_DEVICES = 10;   // 最大扫描设备数量
static const int SCAN_DURATION_MS = 3000; // 扫描持续时间：3秒
static const int LINK_CYCLE_SCAN_DURATION_MS = 200; // Link cycle扫描持续时间：200ms

// Advertising related constants
#define LINK_PROTOCOL_MANUFACTURER_ID_V1 0x01
#define LINK_PROTOCOL_MANUFACTURER_ID_V2 0x02
#define LINK_PROTOCOL_MANUFACTURER_ID_V3 0x03

// Advertising state (currently unused but kept for future use)
// static bool g_advertising_active = false;

#if CONFIG_BT_NIMBLE_EXT_ADV
// Extended advertising state
static bool g_ext_advertising_active = false;
static uint8_t g_ext_adv_instance_id = 0;
#endif

// Forward declarations
extern "C" void ble_app_scan(void);
extern "C" int scan_linkcid(void);
extern "C" int scan_linkcid_cycle(void);

// --- BLE scan result aggregation for MCP tool ---
struct BleDeviceInfo
{
    std::string name;
    std::string addr;
    int rssi;
    std::string linkcid;  // 添加linkcid字段
};
static std::vector<BleDeviceInfo> g_ble_scan_results;
static SemaphoreHandle_t g_ble_scan_done_sem = nullptr;
static std::mutex g_ble_results_mutex;

// --- BLE5 Extended scan result aggregation ---
static std::vector<BleDeviceInfo> g_ble_ext_scan_results;
static SemaphoreHandle_t g_ble_ext_scan_done_sem = nullptr;
static std::mutex g_ble_ext_results_mutex;
static int ext_scan_device_count = 0;

// Current device name storage
static std::string g_current_device_name = "BLE-Scan-Client";

// Get current timestamp as formatted string "yyyy-mm-dd hh:mm"
static std::string get_current_timestamp_string() {
    struct timeval tv;
    gettimeofday(&tv, NULL);

    struct tm *timeinfo = localtime(&tv.tv_sec);
    char buffer[20];
    strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M", timeinfo);

    return std::string(buffer);
}

// LinkCID entry with timestamp
struct LinkCIDEntry {
    std::string linkcid;
    std::string timestamp;  // Formatted timestamp string "yyyy-mm-dd hh:mm"

    LinkCIDEntry(const std::string& cid, const std::string& ts) : linkcid(cid), timestamp(ts) {}
};

// LinkCID list storage for upload
static std::vector<LinkCIDEntry> g_linkcid_list;
static std::mutex g_linkcid_list_mutex;

// Helper function to check if linkcid already exists in the list
static bool linkcid_exists_in_list(const std::string& linkcid) {
    for (const auto& entry : g_linkcid_list) {
        if (entry.linkcid == linkcid) {
            return true;
        }
    }
    return false;
}

// Helper function to add linkcid to list if it doesn't already exist
static void add_linkcid_if_not_exists(const std::string& linkcid) {
    if (!linkcid.empty()) {
        std::lock_guard<std::mutex> lk(g_linkcid_list_mutex);
        if (!linkcid_exists_in_list(linkcid)) {
            g_linkcid_list.emplace_back(linkcid, get_current_timestamp_string());
        }
    }
}

// Current LinkCID for broadcasting
static std::string g_current_broadcast_linkcid = "QmatnPJPAq8tSDGvrWAGqTjhDyaZMUHPXE8GVGyiTZuYoV";
static std::mutex g_broadcast_linkcid_mutex;

static inline std::string fmt_addr_str(const ble_addr_t *addr)
{
    char buf[18];
    const uint8_t *a = addr->val;
    snprintf(buf, sizeof(buf), "%02X:%02X:%02X:%02X:%02X:%02X", a[5], a[4], a[3], a[2], a[1], a[0]);
    return std::string(buf);
}

// 从制造商数据中提取LinkCID
static std::string extract_linkcid_from_mfg_data(const uint8_t *mfg_data, int mfg_data_len)
{
    // 检查制造商数据格式：[制造商ID 2字节][LinkCID字符串]
    if (mfg_data_len < 3) {
        return std::string("");
    }

    // 检查制造商ID是否为0xFFFF (LinkCID广播使用的ID)
    uint16_t mfg_id = (mfg_data[1] << 8) | mfg_data[0];
    if (mfg_id != 0xFFFF) {
        return std::string("");
    }

    // 提取LinkCID字符串 (跳过2字节制造商ID)
    int linkcid_len = mfg_data_len - 2;
    if (linkcid_len > 0) {
        return std::string((const char*)(mfg_data + 2), linkcid_len);
    }

    return std::string("");
}

// 验证设备是否符合LINK协议格式：必须有name和符合0xFFFF+link+version格式的UUID
static bool is_valid_link_device(const BleDeviceInfo& device)
{
    // 必须有设备名称
    if (device.name.empty()) {
        return false;
    }

    // 必须有LinkCID（从制造商数据中提取）
    if (device.linkcid.empty()) {
        return false;
    }

    // LinkCID应该包含"link"字符串和版本信息
    // 格式检查：LinkCID应该是从0xFFFF制造商ID后提取的数据
    // 这里我们检查LinkCID是否包含有效的link协议标识
    std::string linkcid_lower = device.linkcid;
    std::transform(linkcid_lower.begin(), linkcid_lower.end(), linkcid_lower.begin(), ::tolower);

    // 检查是否包含"link"字符串（不区分大小写）

    // 或者检查是否是有效的哈希格式（如IPFS哈希）
    if (device.linkcid.length() >= 10 &&
        (device.linkcid.substr(0, 2) == "Qm" || device.linkcid.substr(0, 2) == "baf")) {
        return true;
    }

    return false;
}

// Start scan and block until complete, return JSON string with results
std::string ble_scan_start_and_wait_json()
{
    {
        std::lock_guard<std::mutex> lk(g_ble_results_mutex);
        g_ble_scan_results.clear();
    }
    if (g_ble_scan_done_sem == nullptr)
    {
        g_ble_scan_done_sem = xSemaphoreCreateBinary();
    }
    else
    {
        xQueueReset(g_ble_scan_done_sem);
    }
    ble_app_scan();
    // Wait until DISC_COMPLETE event or timeout slightly longer than scan duration
    TickType_t wait_ticks = pdMS_TO_TICKS(SCAN_DURATION_MS + 1000);
    xSemaphoreTake(g_ble_scan_done_sem, wait_ticks);

    // Build JSON
    cJSON *root = cJSON_CreateObject();
    cJSON *devices = cJSON_CreateArray();
    int count = 0;
    {
        std::lock_guard<std::mutex> lk(g_ble_results_mutex);
        count = (int)g_ble_scan_results.size();
        for (const auto &d : g_ble_scan_results)
        {
            cJSON *obj = cJSON_CreateObject();
            cJSON_AddStringToObject(obj, "name", d.name.c_str());
            cJSON_AddStringToObject(obj, "addr", d.addr.c_str());
            cJSON_AddNumberToObject(obj, "rssi", d.rssi);
            cJSON_AddStringToObject(obj, "linkcid", d.linkcid.c_str());
            cJSON_AddItemToArray(devices, obj);
        }
    }
    cJSON_AddBoolToObject(root, "success", true);
    cJSON_AddNumberToObject(root, "count", count);
    cJSON_AddItemToObject(root, "devices", devices);

    char *json_str = cJSON_PrintUnformatted(root);
    std::string result(json_str ? json_str : "{}");
    if (json_str)
        cJSON_free(json_str);
    cJSON_Delete(root);
    ESP_LOGD("GAP", "BLE scan result: %s", result.c_str());
    return result;
}

// Start extended scan and block until complete, return JSON string with results including linkcid
std::string ble_scan_linkcid_start_and_wait_json()
{
    {
        std::lock_guard<std::mutex> lk(g_ble_ext_results_mutex);
        g_ble_ext_scan_results.clear();
    }
    if (g_ble_ext_scan_done_sem == nullptr)
    {
        g_ble_ext_scan_done_sem = xSemaphoreCreateBinary();
    }
    else
    {
        xQueueReset(g_ble_ext_scan_done_sem);
    }
    scan_linkcid();
    // Wait until DISC_COMPLETE event or timeout slightly longer than scan duration
    TickType_t wait_ticks = pdMS_TO_TICKS(SCAN_DURATION_MS + 1000);
    xSemaphoreTake(g_ble_ext_scan_done_sem, wait_ticks);

    // Build JSON
    cJSON *root = cJSON_CreateObject();
    cJSON *devices = cJSON_CreateArray();
    int count = 0;
    {
        std::lock_guard<std::mutex> lk(g_ble_ext_results_mutex);
        count = (int)g_ble_ext_scan_results.size();
        for (const auto &d : g_ble_ext_scan_results)
        {
            cJSON *obj = cJSON_CreateObject();
            cJSON_AddStringToObject(obj, "name", d.name.c_str());
            cJSON_AddStringToObject(obj, "addr", d.addr.c_str());
            cJSON_AddNumberToObject(obj, "rssi", d.rssi);
            cJSON_AddStringToObject(obj, "linkcid", d.linkcid.c_str());
            cJSON_AddItemToArray(devices, obj);
        }
    }
    cJSON_AddBoolToObject(root, "success", true);
    cJSON_AddNumberToObject(root, "count", count);
    cJSON_AddItemToObject(root, "devices", devices);

    char *json_str = cJSON_PrintUnformatted(root);
    std::string result(json_str ? json_str : "{}");
    if (json_str)
        cJSON_free(json_str);
    cJSON_Delete(root);
    ESP_LOGD("GAP", "BLE extended scan result: %s", result.c_str());
    return result;
}

// Link cycle specific scan function with 200ms duration
std::string ble_scan_linkcid_cycle_json()
{
    {
        std::lock_guard<std::mutex> lk(g_ble_ext_results_mutex);
        g_ble_ext_scan_results.clear();
    }
    if (g_ble_ext_scan_done_sem == nullptr)
    {
        g_ble_ext_scan_done_sem = xSemaphoreCreateBinary();
    }
    else
    {
        xQueueReset(g_ble_ext_scan_done_sem);
    }

    // Start scan with 200ms duration for link cycle
    scan_linkcid_cycle();

    // Wait until DISC_COMPLETE event or timeout slightly longer than scan duration
    TickType_t wait_ticks = pdMS_TO_TICKS(LINK_CYCLE_SCAN_DURATION_MS + 500);
    xSemaphoreTake(g_ble_ext_scan_done_sem, wait_ticks);

    // Build JSON
    cJSON *root = cJSON_CreateObject();
    cJSON *devices = cJSON_CreateArray();
    int count = 0;
    {
        std::lock_guard<std::mutex> lk(g_ble_ext_results_mutex);
        count = (int)g_ble_ext_scan_results.size();
        for (const auto &d : g_ble_ext_scan_results)
        {
            cJSON *obj = cJSON_CreateObject();
            cJSON_AddStringToObject(obj, "name", d.name.c_str());
            cJSON_AddStringToObject(obj, "addr", d.addr.c_str());
            cJSON_AddNumberToObject(obj, "rssi", d.rssi);
            cJSON_AddStringToObject(obj, "linkcid", d.linkcid.c_str());
            cJSON_AddItemToArray(devices, obj);
        }
    }
    cJSON_AddBoolToObject(root, "success", true);
    cJSON_AddNumberToObject(root, "count", count);
    cJSON_AddItemToObject(root, "devices", devices);

    char *json_str = cJSON_PrintUnformatted(root);
    std::string result(json_str ? json_str : "{}");
    if (json_str)
        cJSON_free(json_str);
    cJSON_Delete(root);
    ESP_LOGD("GAP", "BLE extended scan cycle result: %s", result.c_str());
    return result;
}

// Get linkcid list for upload and clear the list
std::string get_linkcidlist_notupload()
{
    std::lock_guard<std::mutex> lk(g_linkcid_list_mutex);

    if (g_linkcid_list.empty()) {
        return "{\"null\"}";
    }

    // Build JSON with linkcid as key and timestamp string as value
    cJSON *root = cJSON_CreateObject();

    for (const auto& entry : g_linkcid_list) {
        cJSON_AddStringToObject(root, entry.linkcid.c_str(), entry.timestamp.c_str());
    }

    char *json_str = cJSON_PrintUnformatted(root);
    std::string result(json_str ? json_str : "{}");
    if (json_str)
        cJSON_free(json_str);
    cJSON_Delete(root);

    // Clear the list after creating JSON
    g_linkcid_list.clear();

    return result;
}

// Set the LinkCID for broadcasting
std::string set_broadcast_linkcid(const std::string& new_linkcid)
{
    if (new_linkcid.empty()) {
        return "{\"success\": false, \"message\": \"LinkCID cannot be empty\"}";
    }

    {
        std::lock_guard<std::mutex> lk(g_broadcast_linkcid_mutex);
        g_current_broadcast_linkcid = new_linkcid;
    }

    cJSON *root = cJSON_CreateObject();
    cJSON_AddBoolToObject(root, "success", true);
    cJSON_AddStringToObject(root, "message", "LinkCID updated successfully");
    cJSON_AddStringToObject(root, "linkcid", new_linkcid.c_str());

    char *json_str = cJSON_PrintUnformatted(root);
    std::string result(json_str ? json_str : "{\"success\": false}");
    if (json_str)
        cJSON_free(json_str);
    cJSON_Delete(root);

    return result;
}

// BLE5 Extended scan event handling
static int ble_gap_ext_event(struct ble_gap_event *event, void *arg)
{
    switch (event->type)
    {
#if CONFIG_BT_NIMBLE_EXT_ADV
    case BLE_GAP_EVENT_EXT_DISC:
        {
            struct ble_gap_ext_disc_desc *ext_disc = &event->ext_disc;

            // 解析广播数据
            struct ble_hs_adv_fields fields;
            memset(&fields, 0, sizeof(fields));
            int rc = ble_hs_adv_parse_fields(&fields, ext_disc->data, ext_disc->length_data);
            if (rc == 0) {
                // 提取设备名称
                std::string device_name = "";
                if (fields.name_len > 0 && fields.name) {
                    device_name = std::string((const char*)fields.name, fields.name_len);
                }

                // 提取LinkCID
                std::string linkcid = "";
                if (fields.mfg_data_len > 0 && fields.mfg_data) {
                    linkcid = extract_linkcid_from_mfg_data(fields.mfg_data, fields.mfg_data_len);
                    // Add linkcid with timestamp to list if not already exists
                    add_linkcid_if_not_exists(linkcid);
                }

                // 记录扫描结果 - 添加过滤逻辑
                BleDeviceInfo info;
                info.name = device_name;
                info.addr = fmt_addr_str(&ext_disc->addr);
                info.rssi = ext_disc->rssi;
                info.linkcid = linkcid;

                // 过滤条件：必须有name和uuid(0xFFFF+link+version)
                if (is_valid_link_device(info)) {
                    {
                        std::lock_guard<std::mutex> lk(g_ble_ext_results_mutex);
                        g_ble_ext_scan_results.push_back(std::move(info));
                    }
                }
            } else {
                // 尝试在原始数据中查找制造商数据
                std::string linkcid = "";
                for (int i = 0; i < ext_disc->length_data - 3; i++) {
                    // 查找制造商数据类型 (0xFF)
                    if (ext_disc->data[i+1] == 0xFF && i + ext_disc->data[i] + 1 <= ext_disc->length_data) {
                        uint8_t mfg_len = ext_disc->data[i] - 1; // 减去类型字节
                        if (mfg_len >= 2) {
                            // 检查制造商ID是否为0xFFFF
                            uint16_t mfg_id = (ext_disc->data[i+3] << 8) | ext_disc->data[i+2];
                            if (mfg_id == 0xFFFF && mfg_len > 2) {
                                linkcid = std::string((const char*)&ext_disc->data[i+4], mfg_len - 2);
                                // Add linkcid with timestamp to list if not already exists
                                add_linkcid_if_not_exists(linkcid);
                                break;
                            }
                        }
                    }
                }

                // 记录扫描结果（即使解析失败）- 添加过滤逻辑
                BleDeviceInfo info;
                info.name = std::string(""); // 无法解析名称
                info.addr = fmt_addr_str(&ext_disc->addr);
                info.rssi = ext_disc->rssi;
                info.linkcid = linkcid;

                // 过滤条件：必须有name和uuid(0xFFFF+link+version)
                if (is_valid_link_device(info)) {
                    {
                        std::lock_guard<std::mutex> lk(g_ble_ext_results_mutex);
                        g_ble_ext_scan_results.push_back(std::move(info));
                    }
                }
            }

            ext_scan_device_count++;

            // 如果达到最大扫描设备数量，停止扫描
            if (ext_scan_device_count >= MAX_SCAN_DEVICES) {
                ble_gap_disc_cancel();
            }
        }
        break;
#endif

    case BLE_GAP_EVENT_DISC_COMPLETE:
        if (g_ble_ext_scan_done_sem) {
            xSemaphoreGive(g_ble_ext_scan_done_sem);
        }
        break;

    default:
        break;
    }
    return 0;
}



// BLE event handling
static int ble_gap_event(struct ble_gap_event *event, void *arg)
{
    struct ble_hs_adv_fields fields;

    switch (event->type)
    {
    // NimBLE event discovery
    case BLE_GAP_EVENT_DISC:
        ble_hs_adv_parse_fields(&fields, event->disc.data, event->disc.length_data);
        if (fields.name_len > 0)
        {
            // 提取LinkCID
            std::string linkcid = "";
            if (fields.mfg_data_len > 0 && fields.mfg_data) {
                linkcid = extract_linkcid_from_mfg_data(fields.mfg_data, fields.mfg_data_len);
                // Add linkcid with timestamp to list if not already exists
                add_linkcid_if_not_exists(linkcid);
            }

            // record result
            BleDeviceInfo info;
            info.name = std::string(fields.name, fields.name + fields.name_len);
            info.addr = fmt_addr_str(&event->disc.addr);
            info.rssi = event->disc.rssi;
            info.linkcid = linkcid;
            {
                std::lock_guard<std::mutex> lk(g_ble_results_mutex);
                g_ble_scan_results.push_back(std::move(info));
            }
        }
        else
        {
            // 即使没有名称，也尝试解析广播数据以提取LinkCID
            std::string linkcid = "";
            if (fields.mfg_data_len > 0 && fields.mfg_data) {
                linkcid = extract_linkcid_from_mfg_data(fields.mfg_data, fields.mfg_data_len);
                // Add linkcid with timestamp to list if not already exists
                add_linkcid_if_not_exists(linkcid);
            }

            BleDeviceInfo info;
            info.name = std::string("");
            info.addr = fmt_addr_str(&event->disc.addr);
            info.rssi = event->disc.rssi;
            info.linkcid = linkcid;
            {
                std::lock_guard<std::mutex> lk(g_ble_results_mutex);
                g_ble_scan_results.push_back(std::move(info));
            }
        }

        scan_device_count++;

        // 如果达到最大扫描设备数量，停止扫描
        if (scan_device_count >= MAX_SCAN_DEVICES)
        {
            ble_gap_disc_cancel();
        }
        break;

    case BLE_GAP_EVENT_DISC_COMPLETE:
        if (g_ble_scan_done_sem)
        {
            xSemaphoreGive(g_ble_scan_done_sem);
        }
        break;

    default:
        break;
    }
    return 0;
}

// 设置设备名称函数
// int ble_setname(const char *device_name)
// {
//     if (!device_name || strlen(device_name) == 0) {
//         ESP_LOGE("SETNAME", "Invalid device name parameter");
//         return -1;
//     }

//     // 检查名称长度限制
//     if (strlen(device_name) > CONFIG_BT_NIMBLE_GAP_DEVICE_NAME_MAX_LEN) {
//         ESP_LOGE("SETNAME", "Device name too long: %zu (max: %d)",
//                  strlen(device_name), CONFIG_BT_NIMBLE_GAP_DEVICE_NAME_MAX_LEN);
//         return -2;
//     }

//     // 设置GAP服务中的设备名称
//     int rc = ble_svc_gap_device_name_set(device_name);
//     if (rc != 0) {
//         ESP_LOGE("SETNAME", "Failed to set device name: %d", rc);
//         return rc;
//     }

//     // 更新本地存储的设备名称
//     g_current_device_name = std::string(device_name);

//     ESP_LOGI("SETNAME", "Device name set to: %s", device_name);
//     return 0;
// }

extern "C" {

void ble_app_scan(void)
{
    // 重置扫描计数器
    scan_device_count = 0;

    struct ble_gap_disc_params disc_params;
    disc_params.filter_duplicates = 1;
    disc_params.passive = 0;
    disc_params.itvl = 0;
    disc_params.window = 0;
    disc_params.filter_policy = 0;
    disc_params.limited = 0;

    // 使用有限时间扫描而不是永久扫描
    ble_gap_disc(ble_addr_type, SCAN_DURATION_MS, &disc_params, ble_gap_event, NULL);
}

// BLE5扩展广播扫描 - 支持扫描LinkCID
int scan_linkcid(void)
{
#if CONFIG_BT_NIMBLE_EXT_ADV
    // 重置扫描计数器
    ext_scan_device_count = 0;

    // 配置扩展扫描参数
    struct ble_gap_ext_disc_params uncoded_params;
    uncoded_params.itvl = 0;  // 使用默认值
    uncoded_params.window = 0;  // 使用默认值
    uncoded_params.passive = 0;  // 主动扫描

    // 启动扩展扫描
    int rc = ble_gap_ext_disc(ble_addr_type,
                              SCAN_DURATION_MS / 10,  // duration in 10ms units
                              0,  // period (0 = continuous)
                              1,  // filter_duplicates
                              0,  // filter_policy
                              0,  // limited
                              &uncoded_params,  // uncoded_params
                              NULL,  // coded_params (不扫描coded PHY)
                              ble_gap_ext_event,
                              NULL);

    if (rc != 0) {
        ESP_LOGE("GAP", "Failed to start extended scan: %d", rc);
        return rc;
    }

    ESP_LOGI("GAP", "Extended scan for LinkCID started successfully");
    return 0;
#else
    ESP_LOGE("GAP", "BLE5 Extended Advertising not supported");
    return -1;
#endif
}

// BLE5扩展广播扫描 - Link cycle专用，200ms扫描时间
int scan_linkcid_cycle(void)
{
#if CONFIG_BT_NIMBLE_EXT_ADV
    // 重置扫描计数器
    ext_scan_device_count = 0;

    // 配置扩展扫描参数
    struct ble_gap_ext_disc_params uncoded_params;
    uncoded_params.itvl = 0;  // 使用默认值
    uncoded_params.window = 0;  // 使用默认值
    uncoded_params.passive = 0;  // 主动扫描

    // 启动扩展扫描
    int rc = ble_gap_ext_disc(ble_addr_type,
                              LINK_CYCLE_SCAN_DURATION_MS / 10,  // duration in 10ms units
                              0,  // period (0 = continuous)
                              1,  // filter_duplicates
                              0,  // filter_policy
                              0,  // limited
                              &uncoded_params,  // uncoded_params
                              NULL,  // coded_params (不扫描coded PHY)
                              ble_gap_ext_event,
                              NULL);

    if (rc != 0) {
        ESP_LOGE("GAP", "Failed to start extended scan: %d", rc);
        return rc;
    }

    ESP_LOGI("GAP", "Extended scan for LinkCID cycle started successfully");
    return 0;
#else
    ESP_LOGE("GAP", "BLE5 Extended Advertising not supported");
    return -1;
#endif
}

// The application
void ble_app_on_sync(void)
{
    ble_hs_id_infer_auto(0, &ble_addr_type); // Determines the best address type automatically
    ble_app_scan();
}

// The infinite task
void host_task(void *param)
{
    nimble_port_run(); // This function will return only when nimble_port_stop() is executed
}

// BLE广播功能实现

// BLE5扩展广播 - 广播LinkCID
int adv_linkcid(const char *linkcid)
{
    if (!linkcid || strlen(linkcid) == 0) {
        ESP_LOGE("ADV", "Invalid linkcid parameter");
        return -1;
    }

#if CONFIG_BT_NIMBLE_EXT_ADV
    // 如果已经在扩展广播，先停止
    if (g_ext_advertising_active) {
        ble_gap_ext_adv_stop(g_ext_adv_instance_id);
        ble_gap_ext_adv_remove(g_ext_adv_instance_id);
        g_ext_advertising_active = false;
        vTaskDelay(pdMS_TO_TICKS(100));
    }

    int rc;
    struct ble_gap_ext_adv_params ext_adv_params;
    struct ble_hs_adv_fields adv_fields;

    // 清零结构体
    memset(&ext_adv_params, 0, sizeof(ext_adv_params));
    memset(&adv_fields, 0, sizeof(adv_fields));

    // 配置扩展广播参数
    ext_adv_params.connectable = 0;
    ext_adv_params.scannable = 0;
    ext_adv_params.directed = 0;
    ext_adv_params.high_duty_directed = 0;
    ext_adv_params.legacy_pdu = 0;
    ext_adv_params.anonymous = 0;
    ext_adv_params.include_tx_power = 0;
    ext_adv_params.scan_req_notif = 0;
    ext_adv_params.itvl_min = 160;
    ext_adv_params.itvl_max = 160;
    ext_adv_params.channel_map = 0;
    ext_adv_params.filter_policy = 0;
    ext_adv_params.primary_phy = BLE_HCI_LE_PHY_1M;
    ext_adv_params.secondary_phy = BLE_HCI_LE_PHY_1M;
    ext_adv_params.tx_power = 127;
    ext_adv_params.sid = 0;

    // 配置扩展广播实例
    rc = ble_gap_ext_adv_configure(g_ext_adv_instance_id, &ext_adv_params, NULL, NULL, NULL);
    if (rc != 0) {
        //ESP_LOGE("ADV", "Failed to configure extended advertising: %d", rc);
        return rc;
    }

    // 设置广播数据
    adv_fields.flags = BLE_HS_ADV_F_DISC_GEN | BLE_HS_ADV_F_BREDR_UNSUP;

    // 准备LinkCID数据
    size_t linkcid_len = strlen(linkcid);
    if (linkcid_len > 1600) {
        //ESP_LOGE("ADV", "LinkCID too long: %zu bytes (max 1600)", linkcid_len);
        return -1;
    }

    // 将LinkCID作为制造商数据
    static uint8_t mfg_data[1652];
    mfg_data[0] = 0xFF;  // 制造商ID低字节
    mfg_data[1] = 0xFF;  // 制造商ID高字节
    memcpy(&mfg_data[2], linkcid, linkcid_len);

    adv_fields.mfg_data = mfg_data;
    adv_fields.mfg_data_len = linkcid_len + 2;

    // 创建广播数据 - 直接构建扩展广播数据
    struct os_mbuf *data_mbuf = os_msys_get_pkthdr(0, 0);
    if (!data_mbuf) {
        ESP_LOGE("ADV", "Failed to create mbuf");
        return -1;
    }

    // 手动构建广播数据包
    uint8_t flags_data[] = {0x02, 0x01, 0x06};  // Flags: General Discoverable + BR/EDR Not Supported
    rc = os_mbuf_append(data_mbuf, flags_data, sizeof(flags_data));
    if (rc != 0) {
        ESP_LOGE("ADV", "Failed to append flags: %d", rc);
        os_mbuf_free_chain(data_mbuf);
        return rc;
    }

    // 添加制造商数据
    uint8_t mfg_header[] = {
        (uint8_t)(adv_fields.mfg_data_len + 1),  // Length
        0xFF                                      // Manufacturer Data Type
    };
    rc = os_mbuf_append(data_mbuf, mfg_header, sizeof(mfg_header));
    if (rc != 0) {
        ESP_LOGE("ADV", "Failed to append mfg header: %d", rc);
        os_mbuf_free_chain(data_mbuf);
        return rc;
    }

    rc = os_mbuf_append(data_mbuf, adv_fields.mfg_data, adv_fields.mfg_data_len);
    if (rc != 0) {
        ESP_LOGE("ADV", "Failed to append mfg data: %d", rc);
        os_mbuf_free_chain(data_mbuf);
        return rc;
    }

    // 设置扩展广播数据
    rc = ble_gap_ext_adv_set_data(g_ext_adv_instance_id, data_mbuf);
    if (rc != 0) {
        ESP_LOGE("ADV", "Failed to set extended advertising data: %d", rc);
        os_mbuf_free_chain(data_mbuf);
        return rc;
    }

    // 开始扩展广播
    rc = ble_gap_ext_adv_start(g_ext_adv_instance_id, 0, 0);
    if (rc != 0) {
        ESP_LOGE("ADV", "Failed to start extended advertising: %d", rc);
        return rc;
    }

    g_ext_advertising_active = true;
    ESP_LOGI("ADV", "LinkCID advertising started: %s (%zu bytes)", linkcid, linkcid_len);

    return 0;
#else
    ESP_LOGE("ADV", "BLE5 Extended Advertising not supported");
    return -1;
#endif
}

// Link cycle task implementation
void link_cycle_task(void *param)
{
    //ble_setname("LinkCID");

    while (1) {
        // Get current LinkCID for broadcasting
        std::string current_linkcid;
        {
            std::lock_guard<std::mutex> lk(g_broadcast_linkcid_mutex);
            current_linkcid = g_current_broadcast_linkcid;
        }
        // Phase 1: Broadcast for 200ms + random delay (0-20ms)
        uint32_t random_delay = esp_random() % 21; // 0-20ms random delay
        uint32_t broadcast_duration = 200 + random_delay;

        // Start advertising
        int adv_result = adv_linkcid(current_linkcid.c_str());
        if (adv_result == 0) {
            // Wait for broadcast duration
            vTaskDelay(pdMS_TO_TICKS(broadcast_duration));

            // Stop advertising
#if CONFIG_BT_NIMBLE_EXT_ADV
            if (g_ext_advertising_active) {
                ble_gap_ext_adv_stop(g_ext_adv_instance_id);
                ble_gap_ext_adv_remove(g_ext_adv_instance_id);
                g_ext_advertising_active = false;
            }
#endif
        } else {
            vTaskDelay(pdMS_TO_TICKS(broadcast_duration));
        }

        // Phase 2: Scan for 200ms

        // Start 200ms scan directly
#if CONFIG_BT_NIMBLE_EXT_ADV
        // 重置扫描计数器
        ext_scan_device_count = 0;

        // 配置扩展扫描参数
        struct ble_gap_ext_disc_params uncoded_params;
        uncoded_params.itvl = 0;  // 使用默认值
        uncoded_params.window = 0;  // 使用默认值
        uncoded_params.passive = 0;  // 主动扫描

        // 启动扩展扫描 - 200ms
        int rc = ble_gap_ext_disc(ble_addr_type,
                                  LINK_CYCLE_SCAN_DURATION_MS / 10,  // duration in 10ms units (200ms = 20)
                                  0,  // period (0 = continuous)
                                  1,  // filter_duplicates
                                  0,  // filter_policy
                                  0,  // limited
                                  &uncoded_params,  // uncoded_params
                                  NULL,  // coded_params (不扫描coded PHY)
                                  ble_gap_ext_event,
                                  NULL);

        if (rc == 0) {
            // Wait for scan to complete (200ms + some buffer)
            vTaskDelay(pdMS_TO_TICKS(LINK_CYCLE_SCAN_DURATION_MS + 100));
        } else {
            vTaskDelay(pdMS_TO_TICKS(LINK_CYCLE_SCAN_DURATION_MS));
        }
#else
        vTaskDelay(pdMS_TO_TICKS(LINK_CYCLE_SCAN_DURATION_MS));
#endif

        // Phase 3: Sleep for 200ms
        vTaskDelay(pdMS_TO_TICKS(200));
    }
}

}